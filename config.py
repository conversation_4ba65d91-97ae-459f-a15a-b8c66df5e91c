"""
Configuration for live translation system
"""
import os
from typing import Optional

# Gemini API
GEMINI_API_KEY = "AIzaSyD7Z3nlUFqsPmGZlBB4eHm6sSAc3ThtveE" # os.getenv("GEMINI_API_KEY", "AIzaSyD7Z3nlUFqsPmGZlBB4eHm6sSAc3ThtveE")
GEMINI_MODEL = "gemini-2.5-flash-preview-native-audio-dialog"

# Cloudflare Stream API
CLOUDFLARE_API_TOKEN = os.getenv("CLOUDFLARE_API_TOKEN", "")
CLOUDFLARE_ACCOUNT_ID = os.getenv("CLOUDFLARE_ACCOUNT_ID", "")
CLOUDFLARE_STREAM_API_BASE = "https://api.cloudflare.com/client/v4"

# WebSocket server
SERVER_HOST = "0.0.0.0"
SERVER_PORT = 8766

# Audio settings
INPUT_SAMPLE_RATE = 16000
OUTPUT_SAMPLE_RATE = 24000
AUDIO_CHUNK_SIZE_MS = 50

# Video settings
VIDEO_FPS = 30
VIDEO_BUFFER_SECONDS = 5  # Buffer video for 5 seconds
VIDEO_CODEC = "libx264"
AUDIO_CODEC = "aac"

# File paths
TEMP_DIR = "./temp"
RECORDINGS_DIR = "./recordings"
ORIGINAL_AUDIO_DIR = "./recordings/original_audio"
ORIGINAL_VIDEO_DIR = "./recordings/original_video"
TRANSLATED_AUDIO_DIR = "./recordings/translated_audio"
FINAL_VIDEO_DIR = "./recordings/final_video"

# Buffering
DEFAULT_BUFFER_SIZE_SECONDS = 3
MAX_BUFFER_SIZE_SECONDS = 10
ADAPTIVE_BUFFER_THRESHOLD_MS = 2000  # If translation takes more than 2 sec, increase buffer

# Logging
LOG_LEVEL = "DEBUG"
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Limits
MAX_STREAM_DURATION_HOURS = 8
MAX_VIDEO_BITRATE_KBPS = 5000
MAX_AUDIO_BITRATE_KBPS = 128

def ensure_directories():
    """Creates necessary directories if they don't exist"""
    import os
    directories = [
        TEMP_DIR,
        RECORDINGS_DIR,
        ORIGINAL_AUDIO_DIR,
        ORIGINAL_VIDEO_DIR,
        TRANSLATED_AUDIO_DIR,
        FINAL_VIDEO_DIR
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
