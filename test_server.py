#!/usr/bin/env python3
"""
Test script to debug server startup issues
"""
import asyncio
import logging
import sys
import traceback

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stdout
)
logger = logging.getLogger(__name__)

async def test_server():
    """Test server startup step by step"""
    try:
        logger.info("=== Starting server test ===")
        
        # Test imports
        logger.info("Testing imports...")
        import config
        logger.info("config imported")
        
        from video_processor import VideoProcessor
        logger.info("VideoProcessor imported")
        
        from cloudflare_stream import CloudflareStreamClient
        logger.info("CloudflareStreamClient imported")
        
        from buffer_manager import buffer_manager
        logger.info("buffer_manager imported")
        
        from google import genai
        from google.genai import types
        logger.info("genai imported")
        
        import websockets
        logger.info("websockets imported")
        
        # Test Gemini client
        logger.info("Creating Gemini client...")
        client = genai.Client(api_key=config.GEMINI_API_KEY, http_options=types.HttpOptions(api_version="v1alpha"))
        logger.info("Gemini client created")
        
        # Test WebSocket server
        logger.info("Testing WebSocket server...")
        
        async def simple_handler(websocket):
            logger.info(f"Connection from {websocket.remote_address}")
            try:
                async for message in websocket:
                    logger.info(f"Received message: {type(message)}")
                    break
            except Exception as e:
                logger.error(f"Handler error: {e}")
            logger.info("Handler finished")
        
        logger.info("Starting WebSocket server on localhost:8766...")
        async with websockets.serve(simple_handler, "localhost", 8766):
            logger.info("Server started successfully!")
            await asyncio.sleep(30)  # Run for 30 seconds
            
    except Exception as e:
        logger.error(f"Test failed: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    try:
        asyncio.run(test_server())
    except KeyboardInterrupt:
        logger.info("Test stopped by user")
    except Exception as e:
        logger.error(f"Test error: {e}")
        traceback.print_exc()
