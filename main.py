from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from sqlmodel import Field, SQLModel, Session, create_engine, select
from typing import Optional, List
from contextlib import asynccontextmanager
from scalar_fastapi import get_scalar_api_reference

app = FastAPI()

# Database setup (SQLite for now, easy to switch to Postgres)
DATABASE_URL = "sqlite:///./db.sqlite3"
engine = create_engine(DATABASE_URL, echo=True)

# Models
class Host(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    full_name: str
    preferred_language: str
    voice_sample_url: Optional[str] = None

class Room(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    host_id: int = Field(foreign_key="host.id")
    title: str
    host_language: str
    max_languages: int = 1
    status: str = "created"  # created, live, ended

class Guest(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    display_name: Optional[str] = None
    room_id: int = Field(foreign_key="room.id")

class RoomLanguage(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    room_id: int = Field(foreign_key="room.id")
    language: str
    stream_url: Optional[str] = None
    cloudflare_video_uid: Optional[str] = None
    status: str = "pending"  # pending, processing, ready, error

class TranslationSession(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    session_id: str = Field(unique=True)
    room_id: int = Field(foreign_key="room.id")
    target_language: str
    original_audio_path: Optional[str] = None
    original_video_path: Optional[str] = None
    translated_audio_path: Optional[str] = None
    final_video_path: Optional[str] = None
    cloudflare_video_uid: Optional[str] = None
    status: str = "active"  # active, completed, error
    created_at: Optional[str] = None
    completed_at: Optional[str] = None

# Dependency

def get_session():
    with Session(engine) as session:
        yield session

@asynccontextmanager
async def lifespan(app: FastAPI):
    SQLModel.metadata.create_all(engine)
    yield

app = FastAPI(lifespan=lifespan, docs_url=None, redoc_url=None)

from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html, get_swagger_ui_oauth2_redirect_html
from fastapi.responses import HTMLResponse
from fastapi import Request

@app.get("/docs", include_in_schema=False)
def custom_scalar_docs(request: Request):
    return get_scalar_api_reference(
        openapi_url=app.openapi_url,
        title=app.title,
    )

# Endpoints
@app.post("/hosts/", response_model=Host)
def create_host(host: Host, session: Session = Depends(get_session)):
    session.add(host)
    session.commit()
    session.refresh(host)
    return host

@app.post("/rooms/", response_model=Room)
def create_room(room: Room, session: Session = Depends(get_session)):
    # Check host exists
    host = session.get(Host, room.host_id)
    if not host:
        raise HTTPException(status_code=404, detail="Host not found")
    session.add(room)
    session.commit()
    session.refresh(room)
    return room

@app.post("/rooms/{room_id}/go_live", response_model=Room)
def go_live(room_id: int, session: Session = Depends(get_session)):
    room = session.get(Room, room_id)
    if not room:
        raise HTTPException(status_code=404, detail="Room not found")
    room.status = "live"
    session.add(room)
    session.commit()
    session.refresh(room)
    return room

@app.post("/rooms/{room_id}/guests/", response_model=Guest)
def join_room(room_id: int, guest: Guest, session: Session = Depends(get_session)):
    room = session.get(Room, room_id)
    if not room or room.status != "live":
        raise HTTPException(status_code=400, detail="Room not live or not found")
    guest.room_id = room_id
    session.add(guest)
    session.commit()
    session.refresh(guest)
    return guest

@app.patch("/guests/{guest_id}", response_model=Guest)
def update_guest(guest_id: int, guest: Guest, session: Session = Depends(get_session)):
    db_guest = session.get(Guest, guest_id)
    if not db_guest:
        raise HTTPException(status_code=404, detail="Guest not found")
    if guest.display_name:
        db_guest.display_name = guest.display_name
    session.add(db_guest)
    session.commit()
    session.refresh(db_guest)
    return db_guest

@app.get("/rooms/{room_id}")
def get_room(room_id: int, session: Session = Depends(get_session)):
    room = session.get(Room, room_id)
    if not room:
        raise HTTPException(status_code=404, detail="Room not found")
    guests = session.exec(select(Guest).where(Guest.room_id == room_id)).all()
    languages = session.exec(select(RoomLanguage).where(RoomLanguage.room_id == room_id)).all()
    return {
        "room": room,
        "guests": guests,
        "languages": languages
    }
