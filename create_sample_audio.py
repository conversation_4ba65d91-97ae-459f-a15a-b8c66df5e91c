#!/usr/bin/env python3
"""
Creates a sample audio file for testing the live translation system
"""
import numpy as np
import soundfile as sf
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_sample_audio():
    """Creates a simple test audio file"""
    
    # Audio parameters
    sample_rate = 16000  # 16kHz as required by Gemini
    duration = 5.0  # 5 seconds
    frequency = 440  # A4 note
    
    # Generate time array
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # Generate a simple sine wave with some modulation to make it more interesting
    audio = np.sin(2 * np.pi * frequency * t) * 0.3
    
    # Add some variation (simple melody)
    for i, freq in enumerate([440, 523, 659, 784, 880]):  # A, C, E, G, A
        start_sample = int(i * sample_rate)
        end_sample = int((i + 1) * sample_rate)
        if end_sample <= len(audio):
            audio[start_sample:end_sample] = np.sin(2 * np.pi * freq * t[start_sample:end_sample]) * 0.3
    
    # Apply fade in/out to avoid clicks
    fade_samples = int(0.1 * sample_rate)  # 100ms fade
    audio[:fade_samples] *= np.linspace(0, 1, fade_samples)
    audio[-fade_samples:] *= np.linspace(1, 0, fade_samples)
    
    # Save as WAV file
    output_file = "sample.wav"
    sf.write(output_file, audio, sample_rate)
    
    logger.info(f"Created sample audio file: {output_file}")
    logger.info(f"Duration: {duration}s, Sample rate: {sample_rate}Hz")
    logger.info("This is a simple tone sequence for testing purposes.")
    logger.info("For real testing, replace with actual speech audio.")
    
    return output_file

if __name__ == "__main__":
    create_sample_audio()
