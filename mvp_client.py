import asyncio
import websockets
import soundfile
import cv2
import json
import io
import logging
import time
from pydub import AudioSegment

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- File paths ---
SOURCE_AUDIO_PATH = "sample.wav"
SOURCE_VIDEO_PATH = "sample.mp4"  # Optional video file
# --- The WebSocket URI of your server ---
WEBSOCKET_URI = "ws://localhost:8766/English"  # Change language as needed
# --- Chunking configuration ---
AUDIO_CHUNK_SIZE_MS = 50  # Send audio in 50ms chunks
VIDEO_FPS = 30  # Video frames per second

async def stream_media_client():
    """
    Connects to the WebSocket server and streams audio and optionally video.
    """
    try:
        logging.info(f"Attempting to connect to {WEBSOCKET_URI}...")
        async with websockets.connect(WEBSOCKET_URI) as websocket:
            logging.info("Successfully connected to the server.")

            # Create tasks for audio and video streaming
            tasks = []

            # Audio streaming task
            if SOURCE_AUDIO_PATH:
                tasks.append(asyncio.create_task(stream_audio(websocket)))

            # Video streaming task (if video file exists)
            try:
                import os
                if SOURCE_VIDEO_PATH and os.path.exists(SOURCE_VIDEO_PATH):
                    tasks.append(asyncio.create_task(stream_video(websocket)))
                    # Send start recording message
                    start_msg = json.dumps({
                        "type": "start_recording",
                        "width": 1280,
                        "height": 720
                    })
                    await websocket.send(start_msg)
            except Exception as e:
                logging.warning(f"Video streaming not available: {e}")

            if not tasks:
                logging.error("No media files to stream")
                return

            # Run all streaming tasks concurrently
            await asyncio.gather(*tasks)

            # Send stop recording message if video was streamed
            if len(tasks) > 1:
                stop_msg = json.dumps({"type": "stop_recording"})
                await websocket.send(stop_msg)

            logging.info("Finished streaming all media.")

    except websockets.exceptions.ConnectionClosed as e:
        logging.error(f"Connection to server failed or was closed: {e}")
    except ConnectionRefusedError:
        logging.error("Connection refused. Is the server running?")
    except Exception as e:
        logging.error(f"An error occurred: {e}")

async def stream_audio(websocket):
    """
    Streams audio file to the WebSocket server.
    """
    try:
        # Load the audio file using pydub
        audio = AudioSegment.from_file(SOURCE_AUDIO_PATH)

        # Convert to mono, 16kHz, and 16-bit PCM
        audio = audio.set_channels(1).set_frame_rate(16000).set_sample_width(2)

        # Convert to raw PCM data
        audio_bytes = audio.raw_data
        sampling_rate = audio.frame_rate
        sample_width = audio.sample_width  # bytes per sample
        logging.info(
            f"Loaded '{SOURCE_AUDIO_PATH}' sr={sampling_rate}Hz ch={audio.channels} sw={sample_width}B, duration≈{len(audio_bytes)/(sampling_rate*sample_width):.2f}s"
        )

        # Calculate chunk size in bytes
        bytes_per_second = sampling_rate * sample_width
        chunk_size_bytes = int(bytes_per_second * (AUDIO_CHUNK_SIZE_MS / 1000))

        logging.info(f"Streaming audio in {AUDIO_CHUNK_SIZE_MS}ms chunks ({chunk_size_bytes} bytes each).")

        # Stream the audio in chunks
        total_bytes_sent = 0
        for i in range(0, len(audio_bytes), chunk_size_bytes):
            chunk = audio_bytes[i:i + chunk_size_bytes]
            await websocket.send(chunk)
            total_bytes_sent += len(chunk)

            # Simulate real-time streaming by waiting for the duration of the chunk
            await asyncio.sleep(AUDIO_CHUNK_SIZE_MS / 1000)

        logging.info(f"Finished streaming audio. Total bytes sent: {total_bytes_sent}.")

        # Signal end of turn so server will ask Gemini to finalize
        try:
            await websocket.send(json.dumps({"type": "end_of_turn"}))
            logging.info("Sent end_of_turn control message.")
            # Keep the connection open briefly to avoid racing the server/Gemini finalization
            await asyncio.sleep(5)
        except Exception as e:
            logging.warning(f"Failed to send end_of_turn control message: {e}")

    except Exception as e:
        logging.error(f"Error streaming audio: {e}")

async def stream_video(websocket):
    """
    Streams video file to the WebSocket server.
    """
    try:
        # Open video file
        cap = cv2.VideoCapture(SOURCE_VIDEO_PATH)
        if not cap.isOpened():
            logging.error(f"Could not open video file: {SOURCE_VIDEO_PATH}")
            return

        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS) or VIDEO_FPS
        frame_delay = 1.0 / fps

        logging.info(f"Streaming video at {fps} FPS")

        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # Encode frame as JPEG
            _, buffer = cv2.imencode('.jpg', frame)
            frame_bytes = buffer.tobytes()

            # Send frame
            await websocket.send(frame_bytes)
            frame_count += 1

            # Maintain frame rate
            await asyncio.sleep(frame_delay)

        cap.release()
        logging.info(f"Finished streaming video. Total frames sent: {frame_count}")

    except Exception as e:
        logging.error(f"Error streaming video: {e}")

if __name__ == "__main__":
    # Make sure you have audio file (and optionally video file) in the same directory
    # or update the SOURCE_AUDIO_PATH and SOURCE_VIDEO_PATH variables.
    try:
        asyncio.run(stream_media_client())
    except FileNotFoundError as e:
        logging.error(f"Error: Media file not found: {e}")
        logging.error("Please ensure the media files exist and paths are correct.")
    except Exception as e:
        logging.error(f"Unexpected error: {e}")